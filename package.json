{"name": "k<PERSON>hna-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"langchain": "^0.3.26", "next": "15.3.2", "openai": "^4.102.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}