'use client';

import { useState, ReactNode, createContext, useContext, useEffect } from 'react';
import Navigation from '@/components/Navigation';

// Create a context for language
export const LanguageContext = createContext<{
  language: 'en' | 'jp';
  setLanguage: (language: 'en' | 'jp') => void;
}>({ language: 'en', setLanguage: () => {} });

// Custom hook to use the language context
export const useLanguage = () => useContext(LanguageContext);

type ChildrenFunction = (props: { language: 'en' | 'jp' }) => ReactNode;

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [language, setLanguage] = useState<'en' | 'jp'>('en');

  // Initialize language from localStorage
  useEffect(() => {
    const storedLanguage = localStorage.getItem('language');
    if (storedLanguage === 'jp') {
      setLanguage('jp');
    }
  }, []);

  return (
    <LanguageContext.Provider value={{ language, setLanguage }}>
              <div style="overflow-wrap: break-word; -webkit-nbsp-mode: space; line-break: after-white-space;">
            <div style="padding-bottom:10px;">
                <font face="Verdana">
                    <b>Aiki Yoga</b>
                </font>
                <a href="https://www.aiki-yoga.net/" style="text-decoration:none;">
                    <img src="https://www.aiki-yoga.net/assets/img/lotus-flower.png" width="50px" style="opacity:0.3;position:relative;bottom:-10px">
                </a>
            </div>
            <a href="https://www.instagram.com/aikimatsu.yoga"  style="text-decoration:none">
                <img src="https://www.aiki-yoga.net/assets/img/instagram.png" width="30px" style="opacity:0.3;margin-right:5px" alt="instagram">
            </a>
            <a href="https://aikiyoga.substack.com" style="text-decoration:none">
                <img src="https://www.aiki-yoga.net/assets/img/substack.png" width="30px" style="opacity:0.3;margin-left:5px;margin-right:5px" alt="substack">
            </a>
            <a href="https://x.com/aiki_yoga" style="text-decoration:none">
                <img src="https://www.aiki-yoga.net/assets/img/twitter-x.png" width="30px" style="opacity:0.3;margin-left:5px">
            </a>
        </div>
      <div className="min-h-screen flex flex-col">
        <Navigation language={language} setLanguage={setLanguage} />
        <main className="flex-1 p-4 sm:p-6 lg:p-8">
          {typeof children === 'function' ? (children as ChildrenFunction)({ language }) : children}
        </main>
        <footer className="bg-[#FFEBCC] dark:bg-gray-800 shadow-inner py-4">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-sm text-gray-500 dark:text-gray-400">
            {language === 'jp'
              ? 'クリシュナAI - バガヴァッド・ギーターの知恵を通じた導き'
              : 'Krishna AI - Guidance through the wisdom of Bhagavad Gita'}
          </div>
        </footer>
      </div>
    </LanguageContext.Provider>
  );
}
