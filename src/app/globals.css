@import "tailwindcss";

:root {
  --background: #FFF3E0;/*Deep Saffron: #FF8000; Very light saffron: #FFF3DE; Saffron: #FFEDCC*/
  --foreground: #401A06;
  --primary: #008080E6; /* Adding a primary color variable */
  --primary-hover: #00a2a2; /* Adding a primary color variable */
  --title-primary: #584B40;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1e180e; /*#0C0A09;*/
    --foreground: #FAFAF9;
    --primary: #00B3B3; /* A lighter version for dark mode */
    --primary-hover: #00e0e0; /* A lighter version for dark mode */
    --title-primary: #FAFAF9;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--title-primary);
}

.nav-title {
  color: var(--primary)!important;
}

/* Container for title */
.krishna-ai-title {
  display: flex;
  align-items: center;
  gap: 0.25em;
}

/* Krishna part */
.krishna-ai-title .krishna, .krishna-self {
  font-family: 'Philosopher', serif;
  font-weight: 700;
  color: var(--title-primary);
}

/* AI part */
.krishna-ai-title .ai {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-style: italic;
  color: #666;
}

/* Krishna part */
.krishna-ai-title .krishna_jp, .krishna-self_jp {
  font-family: 'Kaisei Tokumin', serif;
  font-weight: 700;
  /*letter-spacing: -0.05rem;*/
  color: var(--title-primary);
}

/* AI part */
.krishna-ai-title .ai_jp {
  font-family: 'Kaisei Tokumin', sans-serif;
  font-weight: 700;
  font-style: italic;
  color: #666;
}