import { Chapter } from './bhagavad-gita';

export const chapters: Chapter[] = [
    { chapter: 1, title: 'Arjuna Vishada Yoga', japaneseTitle: 'アルジュナの苦悩のヨーガ' },
    { chapter: 2, title: 'Sankhya Yoga', japaneseTitle: '叡智のヨーガ' },
    { chapter: 3, title: 'Karma Yoga', japaneseTitle: '行為のヨーガ' },
    { chapter: 4, title: 'Jnana Yoga', japaneseTitle: '知識のヨーガ' },
    { chapter: 5, title: 'Karma Sannyasa Yoga', japaneseTitle: '行為の放棄のヨーガ' },
    { chapter: 6, title: 'Dhyana Yoga', japaneseTitle: '瞑想のヨーガ' },
    { chapter: 7, title: 'Vijnana Yoga', japaneseTitle: '知識と智慧のヨーガ' },
    { chapter: 8, title: 'Akshara Brahma Yoga', japaneseTitle: '不滅のブラフマンのヨーガ' },
    { chapter: 9, title: 'Raja Vidya Raja Guhya Yoga', japaneseTitle: '王の知識と王の秘密のヨーガ' },
    { chapter: 10, title: '<PERSON><PERSON><PERSON><PERSON> Yoga', japaneseTitle: '神の顕現のヨーガ' },
    { chapter: 11, title: '<PERSON><PERSON><PERSON><PERSON><PERSON> Darshana Yoga', japaneseTitle: '宇宙の姿のヨーガ' },
    { chapter: 12, title: 'Bhakti Yoga', japaneseTitle: '信愛のヨーガ' },
    { chapter: 13, title: 'Kshetra-Kshetrajna Vibhaga Yoga', japaneseTitle: '場と場の知者の分離のヨーガ' },
    { chapter: 14, title: 'Gunatraya-Vibhaga Yoga', japaneseTitle: '三つのグナの分離のヨーガ' },
    { chapter: 15, title: 'Purushottama Yoga', japaneseTitle: '至高者のヨーガ' },
    { chapter: 16, title: 'Daivasura-Sampad Vibhaga Yoga', japaneseTitle: '神と悪魔の性質の分離のヨーガ' },
    { chapter: 17, title: 'Shraddha-Traya-Vibhaga Yoga', japaneseTitle: '三つの信仰の分離のヨーガ' },
    { chapter: 18, title: 'Moksha-Sannyasa Yoga', japaneseTitle: '解放と放棄のヨーガ' },
];